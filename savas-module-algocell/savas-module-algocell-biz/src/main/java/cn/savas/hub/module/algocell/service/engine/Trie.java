package cn.savas.hub.module.algocell.service.engine;

import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 轻量级Trie树实现
 * 相比Aho-Corasick算法更简单，适合中等规模的术语库
 *
 * <AUTHOR>
 */
@Slf4j
public class Trie {

    /**
     * Trie树节点
     */
    static class Node {
        /**
         * 子节点映射，键为字符，值为子节点
         */
        Map<Character, Node> children = new HashMap<>();

        /**
         * 在此节点结束的术语列表
         * 支持多个术语在同一位置结束的情况
         */
        List<String> outputs = new ArrayList<>();

        /**
         * 判断是否为叶子节点
         *
         * @return 如果没有子节点则返回true
         */
        boolean isLeaf() {
            return children.isEmpty();
        }

        /**
         * 判断是否为终止节点
         *
         * @return 如果有输出术语则返回true
         */
        boolean isTerminal() {
            return !outputs.isEmpty();
        }
    }

    /**
     * 根节点
     */
    private final Node root = new Node();

    /**
     * 术语总数
     */
    private int termCount = 0;

    /**
     * 是否区分大小写
     */
    private final boolean caseSensitive;

    /**
     * 构造函数
     *
     * @param caseSensitive 是否区分大小写
     */
    public Trie(boolean caseSensitive) {
        this.caseSensitive = caseSensitive;
    }

    /**
     * 默认构造函数，区分大小写
     */
    public Trie() {
        this(true);
    }

    /**
     * 插入术语到Trie树
     *
     * @param term 要插入的术语
     */
    public void insert(String term) {
        if (term == null || term.isEmpty()) {
            return;
        }

        String normalizedTerm = caseSensitive ? term : term.toLowerCase();
        Node current = root;

        // 逐字符构建路径
        for (char c : normalizedTerm.toCharArray()) {
            current = current.children.computeIfAbsent(c, k -> new Node());
        }

        // 在终止节点添加术语
        if (!current.outputs.contains(term)) {
            current.outputs.add(term);
            termCount++;
            log.debug("插入术语: {}", term);
        }
    }

    /**
     * 批量插入术语
     *
     * @param terms 术语集合
     */
    public void insertAll(Collection<String> terms) {
        if (terms != null) {
            terms.forEach(this::insert);
        }
    }

    /**
     * 在文本中查找所有匹配的术语
     *
     * @param text 要搜索的文本
     * @return 匹配结果列表，包含起始位置和匹配的术语
     */
    public List<MatchResult> findAll(String text) {
        List<MatchResult> results = new ArrayList<>();

        if (text == null || text.isEmpty()) {
            return results;
        }

        String normalizedText = caseSensitive ? text : text.toLowerCase();

        // 从每个位置开始尝试匹配
        for (int i = 0; i < normalizedText.length(); i++) {
            Node current = root;

            // 从位置i开始向前匹配
            for (int j = i; j < normalizedText.length(); j++) {
                char c = normalizedText.charAt(j);
                current = current.children.get(c);

                if (current == null) {
                    // 无法继续匹配
                    break;
                }

                // 检查是否有术语在此位置结束
                if (current.isTerminal()) {
                    for (String term : current.outputs) {
                        // 验证原始文本中的匹配
                        String originalMatch = text.substring(i, j + 1);
                        if (isValidMatch(term, originalMatch)) {
                            results.add(new MatchResult(i, j + 1, term, originalMatch));
                        }
                    }
                }
            }
        }

        return results;
    }

    /**
     * 在文本中查找所有匹配的术语（去重）
     *
     * 相同的术语在文本中只会被计算一次，但保留重叠匹配（不同术语在同一位置）
     *
     * @param text 要搜索的文本
     * @return 去重后的匹配结果列表
     */
    public List<MatchResult> findAllUnique(String text) {
        List<MatchResult> allResults = findAll(text);

        if (allResults.isEmpty()) {
            return allResults;
        }

        // 使用Set来去重相同的术语，但保留第一次出现的位置信息
        Map<String, MatchResult> uniqueTerms = new LinkedHashMap<>();

        for (MatchResult result : allResults) {
            // 如果术语还没有被记录，则添加
            if (!uniqueTerms.containsKey(result.term)) {
                uniqueTerms.put(result.term, result);
            }
        }

        return new ArrayList<>(uniqueTerms.values());
    }

    /**
     * 验证匹配是否有效
     *
     * @param term 术语
     * @param match 匹配的文本
     * @return 是否有效
     */
    private boolean isValidMatch(String term, String match) {
        if (caseSensitive) {
            return term.equals(match);
        } else {
            return term.equalsIgnoreCase(match);
        }
    }

    /**
     * 检查术语是否存在
     *
     * @param term 要检查的术语
     * @return 如果存在返回true
     */
    public boolean contains(String term) {
        if (term == null || term.isEmpty()) {
            return false;
        }

        String normalizedTerm = caseSensitive ? term : term.toLowerCase();
        Node current = root;

        for (char c : normalizedTerm.toCharArray()) {
            current = current.children.get(c);
            if (current == null) {
                return false;
            }
        }

        return current.isTerminal() && current.outputs.contains(term);
    }

    /**
     * 获取术语总数
     *
     * @return 术语数量
     */
    public int getTermCount() {
        return termCount;
    }

    /**
     * 获取节点总数（用于内存使用估算）
     *
     * @return 节点数量
     */
    public int getNodeCount() {
        return countNodes(root);
    }

    /**
     * 递归计算节点数量
     *
     * @param node 当前节点
     * @return 以该节点为根的子树的节点数量
     */
    private int countNodes(Node node) {
        int count = 1; // 当前节点
        for (Node child : node.children.values()) {
            count += countNodes(child);
        }
        return count;
    }

    /**
     * 清空Trie树
     */
    public void clear() {
        root.children.clear();
        root.outputs.clear();
        termCount = 0;
    }

    /**
     * 匹配结果类
     */
    public static class MatchResult {
        /**
         * 起始位置（包含）
         */
        public final int start;

        /**
         * 结束位置（不包含）
         */
        public final int end;

        /**
         * 匹配的术语
         */
        public final String term;

        /**
         * 原始匹配文本
         */
        public final String originalText;

        public MatchResult(int start, int end, String term, String originalText) {
            this.start = start;
            this.end = end;
            this.term = term;
            this.originalText = originalText;
        }

        /**
         * 获取匹配长度
         *
         * @return 匹配的字符数
         */
        public int getLength() {
            return end - start;
        }

        @Override
        public String toString() {
            return String.format("MatchResult{start=%d, end=%d, term='%s', text='%s'}",
                start, end, term, originalText);
        }
    }
}
